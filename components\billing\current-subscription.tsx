"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, CreditCard, AlertCircle, CheckCircle, XCircle } from "lucide-react";
import { authClient } from "@/lib/auth-client";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import { UserPurchasesResponse } from "@/app/api/account/route";

interface CurrentSubscription {
  id: string;
  product: string;
  providerCustomerId: string;
  status: string;
  created_at: Date;
  updated_at: Date;
}

export function CurrentSubscription() {
  const { data: session } = authClient.useSession();
  const [isLoading, setIsLoading] = useState(false);
  const [subscription, setSubscription] = useState<CurrentSubscription | null>(null);
  const [fetchingSubscription, setFetchingSubscription] = useState(true);

  // Fetch user's current subscription from database
  const fetchSubscription = async () => {
    if (!session?.user) return;

    try {
      setFetchingSubscription(true);
      const response = await fetch('/api/account');
      if (response.ok) {
        const data: UserPurchasesResponse = await response.json();
        // Get the most recent active subscription
        const activeSubscription = data.subscriptions.find(sub => sub.status === 'active') ||
                                 data.subscriptions[0]; // Fallback to first subscription if no active one
        setSubscription(activeSubscription || null);
      } else if (response.status !== 404) {
        // 404 means no subscription, which is fine
        console.error('Failed to fetch subscription');
      }
    } catch (error) {
      console.error('Error fetching subscription:', error);
    } finally {
      setFetchingSubscription(false);
    }
  };

  useEffect(() => {
    fetchSubscription();
  }, [session?.user]);

  const formatDate = (date: Date | string) => {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    return dateObj.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "active":
        return (
          <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200">
            <CheckCircle className="w-3 h-3 mr-1" />
            Active
          </Badge>
        );
      case "past_due":
        return (
          <Badge variant="destructive">
            <AlertCircle className="w-3 h-3 mr-1" />
            Past Due
          </Badge>
        );
      case "canceled":
        return (
          <Badge variant="outline" className="border-red-300 text-red-700 dark:border-red-700 dark:text-red-300">
            <XCircle className="w-3 h-3 mr-1" />
            Canceled
          </Badge>
        );
      case "expired":
        return (
          <Badge variant="outline" className="border-gray-300 text-gray-700 dark:border-gray-700 dark:text-gray-300">
            <XCircle className="w-3 h-3 mr-1" />
            Expired
          </Badge>
        );
      default:
        return (
          <Badge variant="outline">
            {status.charAt(0).toUpperCase() + status.slice(1)}
          </Badge>
        );
    }
  };

  const handleCancelSubscription = async () => {
    if (!subscription) return;

    setIsLoading(true);
    try {
      const response = await fetch(`/api/subscription/cancel?subscription_id=${encodeURIComponent(subscription.id)}`, {
        method: 'POST'
      });

      if (response.ok) {
        toast.success("Subscription cancelled successfully");
        await fetchSubscription(); // Refresh subscription data
      } else {
        throw new Error('Failed to cancel subscription');
      }
    } catch (error) {
      toast.error("Failed to cancel subscription");
    } finally {
      setIsLoading(false);
    }
  };

  const handleCustomerPortal = async () => {
    if (!subscription) return;

    try {
      const response = await fetch(`/api/customerPortal?customer_id=${encodeURIComponent(subscription.providerCustomerId)}`);
      if (!response.ok) throw new Error("Failed to get portal URL");
      const data = await response.json();
      window.location.href = data.url;
    } catch (err) {
      console.error("Error accessing customer portal:", err);
      toast.error("Failed to access customer portal");
    }
  };

  if (fetchingSubscription) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-neutral-900 dark:text-neutral-200">
            <CreditCard className="w-5 h-5 text-[#ffbe98]" />
            Current Subscription
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-neutral-200 dark:bg-neutral-800 rounded w-3/4"></div>
            <div className="h-4 bg-neutral-200 dark:bg-neutral-800 rounded w-1/2"></div>
            <div className="h-10 bg-neutral-200 dark:bg-neutral-800 rounded w-full"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!subscription) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-neutral-900 dark:text-neutral-200">
            <CreditCard className="w-5 h-5 text-[#ffbe98]" />
            Current Subscription
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <CreditCard className="w-12 h-12 text-neutral-400 mx-auto mb-3" />
            <p className="text-neutral-600 dark:text-neutral-400">
              You don't have any active subscriptions yet.
            </p>
            <p className="text-sm text-neutral-500 dark:text-neutral-500 mt-1">
              Choose a plan below to get started.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-3 text-neutral-900 dark:text-neutral-200">
          <CreditCard className="w-5 h-5 text-[#ffbe98]" />
          Current Subscription
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="flex items-start justify-between">
          <div className="space-y-1">
            <h3 className="font-semibold text-lg text-neutral-900 dark:text-neutral-200">
              {subscription.product}
            </h3>
            <p className="text-sm text-neutral-600 dark:text-neutral-400">
              Customer ID: {subscription.providerCustomerId}
            </p>
          </div>
          {getStatusBadge(subscription.status)}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <div className="text-sm text-neutral-500 dark:text-neutral-400">Started</div>
            <div className="text-sm text-neutral-900 dark:text-neutral-200">
              {formatDate(subscription.created_at)}
            </div>
          </div>

          <div className="space-y-2">
            <div className="text-sm text-neutral-500 dark:text-neutral-400">Last Updated</div>
            <div className="flex items-center gap-2 text-sm text-neutral-900 dark:text-neutral-200">
              <Calendar className="w-4 h-4" />
              {formatDate(subscription.updated_at)}
            </div>
          </div>
        </div>

        {subscription.status === "canceled" && (
          <div className="p-3 bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800 rounded-lg">
            <div className="flex items-start gap-3">
              <AlertCircle className="w-5 h-5 text-orange-600 dark:text-orange-400 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-orange-800 dark:text-orange-200">
                  Subscription Cancelled
                </p>
                <p className="text-sm text-orange-700 dark:text-orange-300 mt-1">
                  Your subscription was cancelled on {formatDate(subscription.updated_at)}.
                </p>
              </div>
            </div>
          </div>
        )}

        <div className="flex gap-3 pt-4 border-t border-neutral-200 dark:border-neutral-800">
          {subscription.status === "active" && (
            <Button
              variant="outline"
              onClick={handleCancelSubscription}
              disabled={isLoading}
              className="border-red-300 text-red-700 hover:bg-red-50 dark:border-red-700 dark:text-red-300 dark:hover:bg-red-950/20"
            >
              {isLoading ? "Cancelling..." : "Cancel Subscription"}
            </Button>
          )}

          <Button
            variant="outline"
            onClick={handleCustomerPortal}
            className="bg-[#ffbe98] hover:bg-[#ffbe98]/90 text-white border-[#ffbe98]"
          >
            Manage Subscription
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
